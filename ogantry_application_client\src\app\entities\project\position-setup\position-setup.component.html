<form class="form mt-0" id="positionSetup">
  <div class="position-relative pb-5" data-wizard-type="step-content">
    <div class="card-body scrollable-content">
      <form class="form" [formGroup]="addPositionForm" autocomplete="off" novalidate="novalidate" id="create_project_setup_form">
        <p-table
          responsiveLayout="scroll"
          [scrollHeight]="height"
          #dt
          [value]="position"
          [(selection)]="checkedPosition"
          [selectAll]="true"
          dataKey="position.id"
          [loading]="loading"
          selectionMode="multiple"
          styleClass="p-datatable-customers"
          [filterDelay]="0"
          [style]="{ overflow: 'auto!important' }"
          editMode="row"
          (selectionChange)="listChecked()"
          [sortField]="sortFieldName"
          [sortOrder]="sortOrderNumber"
          (onSort)="sortColumn()"
          [lazy]="true"
          (onLazyLoad)="getPositionList()"
        >
          <ng-template pTemplate="header">
            <tr class="my-2">
              <th [colSpan]="totalColumns" id="plus-icon" class="header-width-plus">
                <div class="d-flex flex-nowrap flex-row align-i justify-content-between">
                  <div class="position-label">
                    <h3 class="position-text">Positions</h3>
                  </div>
                  <div class="d-flex flex-row flex-nowrap">
                    <div *ngIf="listSelected" class="d-flex align-items-center">
                      <a href="javascript:;void" class="form-group btn btn-add-wrapper mr-3 btn-icon btn-light btn-plus btn-sm svg-icon-white update-Date" (click)="showPODialog()">
                        <span title="Update Purchase Order" [inlineSVG]="'assets/media/svg/icons/purchaseOrder.svg'" cacheSVG="true" class="pointer"> </span>
                      </a>
                    </div>
                    <div *ngIf="listSelected" class="d-flex align-items-center edit-date-btn">
                      <a
                        href="javascript:;void"
                        class="btn btn-add-wrapper btn-icon btn-light btn-plus btn-sm form-group mr-3 svg-icon-white update-Date"
                        (click)="showPostionDialog()"
                      >
                        <span title="Update Date" [inlineSVG]="'assets/media/svg/icons/edit_calendar.svg'" cacheSVG="true" class="pointer"> </span>
                      </a>
                    </div>

                    <div class="float-right mr-4 py-2 pointer" #coulmnToggel>
                      <a href="javascript:;void" class="form-group btn btn-icon btn-light btn-plus svg-icon-white add-new-btn" (click)="showFilter = !showFilter">
                        <span title="Add Position" [inlineSVG]="'assets/media/svg/icons/filter.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                      </a>
                    </div>

                    <div class="float-right mr-3 py-2 pointer" (click)="!positionEdit && !showAddPosition && (isShowHideColumns = !isShowHideColumns)" #coulmnToggel>
                      <app-filter-icon-shared></app-filter-icon-shared>
                    </div>

                    <div class="card popup-column">
                      <app-filter-table-fields
                        *ngIf="isShowHideColumns"
                        [selectedColumns]="selectedColumns"
                        [frozenCols]="frozenCols"
                        dynamicBindingKey="monthLabel"
                        (onSelectColumChange)="onSelectColumnChange($event)"
                      ></app-filter-table-fields>
                    </div>
                    <div class="add-new-position py-2">
                      <a href="javascript:;void" class="btn btn-icon btn-light btn-plus form-group add-new-btn svg-icon-white" (click)="showAddPositionRow()">
                        <span title="Add Position" [inlineSVG]="'assets/media/svg/icons/plus.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                      </a>
                      <!-- TODO : Purchase Order feature -->
                      <!-- <a
                          href="javascript:;void"

                          (click)="openPurchaseOrderSidebar()"
                        >
                          <span
                            title="Add Purchase Order"
                            [inlineSVG]="'assets/media/svg/icons/add_purchase_order.svg'"
                            cacheSVG="true"
                            class="svg-icon svg-icon-md"
                          >
                          </span>
                        </a> -->
                    </div>
                  </div>
                </div>
              </th>
            </tr>
            <tr [ngClass]="{ billRate: showBillRate }">
              <th class="checkbox-wrapper">
                <div>
                  <p-checkbox
                    [(ngModel)]="listSelected"
                    [ngModelOptions]="{ standalone: true }"
                    [binary]="true"
                    checkboxIcon="pi pi-minus"
                    (ngModelChange)="listSelected ? selectAllPositionCheck() : removePosition()"
                  ></p-checkbox>
                </div>
              </th>
              <th id="position" class="header-position no-wrap" pSortableColumn="name">
                Position Name
                <p-sortIcon field="name"></p-sortIcon>
              </th>
              <th id="person" class="header-person no-wrap">Assign Person</th>
              <th id="skillSet" class="header-skill no-wrap">Skill Set</th>
              <th id="date" class="header-start no-wrap" pSortableColumn="start_date">
                Start Date
                <p-sortIcon field="start_date"></p-sortIcon>
              </th>
              <th id="date" class="header-end no-wrap" pSortableColumn="end_date">
                End Date
                <p-sortIcon field="end_date"></p-sortIcon>
              </th>
              <th id="allocation" class="header-allocation no-wrap" *ngIf="_pCols?.includes('allocation_daily')" pSortableColumn="daily_billable_hours">
                Allocation (Daily)
                <p-sortIcon field="daily_billable_hours"></p-sortIcon>
              </th>
              <th id="allocation" class="header-allocation no-wrap" *ngIf="_pCols?.includes('allocation_weekly')">Allocation (Weekly)</th>
              <th *ngIf="_pCols?.includes('cost')" id="cost" class="header-cost no-wrap" pSortableColumn="cost">
                Hourly Cost
                <p-sortIcon field="cost"></p-sortIcon>
              </th>
              <th id="expense" *ngIf="_pCols?.includes('expense')" class="header-expense no-wrap">Expense</th>
              <th id="expense" class="header-bill no-wrap" *ngIf="showBillRate && _pCols?.includes('Bill Rate')" pSortableColumn="bill_rate">
                Hourly Bill Rate
                <p-sortIcon field="bill_rate"></p-sortIcon>
              </th>
              <th *ngIf="showBillRate && _pCols?.includes('Margin')" id="margin" class="header-margin no-wrap">Margin</th>
              <th *ngIf="_pCols?.includes('purchase_order')" id="purchase_order" class="header-margin no-wrap">Purchase Order</th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component == componentType">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th class="no-wrap" colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)">
                        {{ filedDetails?.name }}
                      </th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <th id="actions" class="text-center header-width fix-action-width"></th>
            </tr>

            <tr *ngIf="showFilter">
              <th class="checkbox-wrapper"></th>
              <th id="addPosition" class="header-position">
                <input
                  pInputText
                  type="search"
                  appForbiddenName="project_expenses"
                  [(ngModel)]="dataFilter.name"
                  class="p-column-filter form-control-custom"
                  placeholder="Position Name"
                  (input)="getPositionList()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </th>
              <th id="addPerson" class="header-person">
                <p-dropdown
                  (onChange)="getPositionList()"
                  [options]="employeeList"
                  optionValue="id"
                  [(ngModel)]="dataFilter.employee_id"
                  styleClass="p-column-filter pi-icon form-control-custom employee-dropdown"
                  placeholder="Assign to"
                  appendTo="body"
                  [showClear]="true"
                  [ngModelOptions]="{ standalone: true }"
                >
                </p-dropdown>
              </th>
              <th class="header-skill">
                <p-dropdown
                  (onChange)="getPositionList()"
                  [options]="skillSetList"
                  optionValue="id"
                  [(ngModel)]="dataFilter.position_type_id"
                  styleClass="p-column-filter pi-icon form-control-custom skill-set"
                  placeholder="Skill Set"
                  appendTo="body"
                  [showClear]="true"
                  [ngModelOptions]="{ standalone: true }"
                >
                </p-dropdown>
              </th>
              <th id="addStartDate" class="header-start py-1">
                <div class="p-inputgroup date-filter-group">
                  <span *ngIf="dataFilter.start_date" class="p-inputgroup-addon">
                    {{ getSymbol(dataFilter.selectedFilterStartDate) }}
                  </span>
                  <span class="p-input-icon-right cal-drop position-relative w-100" [ngClass]="dataFilter.start_date ? 'date-filter' : ''">
                    <p-calendar
                      #startCal
                      appendTo="body"
                      inputId="startDate"
                      [(ngModel)]="dataFilter.start_date"
                      placeholder="Start Date"
                      showButtonBar="true"
                      (onClearClick)="onClearDateFilter('start_date')"
                      (onSelect)="getPositionList()"
                      (onInput)="getPositionList()"
                      (onClear)="onClearDateFilter('start_date')"
                      (onClose)="getPositionList()"
                      inputStyleClass="p-0"
                      styleClass="date-filter-cal"
                      [ngModelOptions]="{ standalone: true }"
                    >
                      <ng-template pTemplate="header">
                        <span class="cal-drop">
                          <p-dropdown [(ngModel)]="dataFilter.selectedFilterStartDate" [options]="dateFilter" [ngModelOptions]="{ standalone: true }"></p-dropdown>
                        </span>
                      </ng-template>
                    </p-calendar>
                  </span>
                  <span *ngIf="dataFilter.start_date" class="p-inputgroup-cancel">
                    <em class="pi pi-times" (click)="onClearDateFilter('start_date')"></em>
                  </span>
                </div>
              </th>
              <th id="addEndDate" class="header-end py-1">
                <div class="p-inputgroup date-filter-group">
                  <span *ngIf="dataFilter.end_date" class="p-inputgroup-addon">
                    {{ getSymbol(dataFilter.selectedFilterEndDate) }}
                  </span>
                  <span class="p-input-icon-right cal-drop position-relative w-100" [ngClass]="dataFilter.end_date ? 'date-filter' : ''">
                    <p-calendar
                      #startCal
                      appendTo="body"
                      inputId="endDate"
                      [(ngModel)]="dataFilter.end_date"
                      placeholder="End Date"
                      showButtonBar="true"
                      (onClearClick)="onClearDateFilter('end_date')"
                      (onSelect)="getPositionList()"
                      (onClose)="getPositionList()"
                      (onInput)="getPositionList()"
                      (onClear)="onClearDateFilter('end_date')"
                      inputStyleClass="p-0"
                      styleClass="date-filter-cal"
                      [ngModelOptions]="{ standalone: true }"
                    >
                      <ng-template pTemplate="header">
                        <span class="cal-drop">
                          <p-dropdown [(ngModel)]="dataFilter.selectedFilterEndDate" [options]="dateFilter" [ngModelOptions]="{ standalone: true }"></p-dropdown>
                        </span>
                      </ng-template>
                    </p-calendar>
                  </span>
                  <span *ngIf="dataFilter.end_date" class="p-inputgroup-cancel">
                    <em class="pi pi-times" (click)="onClearDateFilter('end_date')"></em>
                  </span>
                </div>
              </th>
              <th id="addAllocation" class="header-allocation" *ngIf="_pCols?.includes('allocation_daily')">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  [(ngModel)]="dataFilter.daily_billable_hours"
                  class="p-column-filter form-control-custom"
                  style="width: 4rem !important"
                  (input)="getPositionList()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </th>
              <th id="allocationWeekly" class="header-allocation" *ngIf="_pCols?.includes('allocation_weekly')"></th>
              <th id="addCost" class="header-cost" *ngIf="_pCols?.includes('cost')">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  [(ngModel)]="dataFilter.cost"
                  class="p-column-filter form-control-custom"
                  style="width: 5rem !important"
                  placeholder="Cost"
                  (input)="getPositionList()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </th>
              <th id="addExpense" *ngIf="_pCols?.includes('expense')" class="header-expense"></th>
              <th id="addBillRate" *ngIf="showBillRate && _pCols?.includes('Bill Rate')" class="header-bill">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  [(ngModel)]="dataFilter.bill_rate"
                  class="p-column-filter form-control-custom"
                  style="width: 6rem !important"
                  placeholder="Bill Rate"
                  (input)="getPositionList()"
                  [ngModelOptions]="{ standalone: true }"
                />
              </th>
              <th *ngIf="showBillRate && _pCols?.includes('Margin')" class="header-margin"></th>

              <th *ngIf="_pCols?.includes('purchase_order')" id="purchase_order" class="header-margin no-wrap"></th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component == componentType">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th class="no-wrap" colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)"></th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>
              <th id="actions" class="text-center header-width fix-action-width"></th>
            </tr>

            <tr *ngIf="showAddPosition">
              <th class="background-white"></th>
              <th id="addPosition" class="background-white">
                <input
                  pInputText
                  #focusAdd
                  type="search"
                  minlength="5"
                  appForbiddenName="project_expenses"
                  formControlName="name"
                  class="form-control-custom p-column-filter"
                  placeholder="Position Name"
                  (change)="checkPotionIsUnique()"
                />
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'name'" [fieldLabel]="'Position'"></app-form-error>
                <app-form-error class="position-rel" [validation]="'minlength'" [form]="addPositionForm" [controlName]="'name'" [fieldLabel]="'Position'"></app-form-error>
                <app-form-error class="position-rel" [validation]="'forbiddenName'" [form]="addPositionForm" [controlName]="'name'" [fieldLabel]="'Position'"></app-form-error>
                <small class="form-text text-danger" *ngIf="this.addPositionForm.get('name').valid && !positionIsUnique"> Position must be Unique </small>
              </th>
              <th id="addPerson" class="background-white">
                <p-dropdown
                  [options]="employeeList"
                  formControlName="employee_id"
                  styleClass="p-column-filter pi-icon form-control-custom"
                  placeholder="Assign to"
                  appendTo="body"
                  (onChange)="employeeSelected($event)"
                >
                </p-dropdown>
                <small class="form-text text-danger" *ngIf="isShowEmployeeError.isErrorInAdd">
                  {{ appConstants.employeeErrorMessage }}
                </small>
              </th>
              <th class="background-white">
                <p-dropdown
                  [options]="skillSetList"
                  formControlName="position_type_id"
                  styleClass="p-column-filter pi-icon form-control-custom"
                  placeholder="Skill Set"
                  appendTo="body"
                >
                </p-dropdown>
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'position_type_id'" [fieldLabel]="'Skill'"></app-form-error>
              </th>
              <th id="addStartDate" class="background-white">
                <p-calendar
                  appendTo="body"
                  [readonlyInput]="false"
                  (onSelect)="startDateSelected()"
                  inputId="startDate"
                  formControlName="start_date"
                  placeholder="Start Date"
                  showButtonBar="true"
                  (onClearClick)="clearStartDate()"
                >
                </p-calendar>
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'start_date'" [fieldLabel]="'Start Date'"></app-form-error>
              </th>
              <th id="addEndDate" class="background-white">
                <p-calendar
                  [readonlyInput]="false"
                  [minDate]="positionMinDate"
                  (onSelect)="endDateSelected()"
                  appendTo="body"
                  inputId="endDate"
                  formControlName="end_date"
                  placeholder="End Date"
                  showButtonBar="true"
                  (onClearClick)="clearEndDate()"
                >
                </p-calendar>
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'end_date'" [fieldLabel]="'End Date'"></app-form-error>
              </th>
              <th id="addAllocation" class="background-white" *ngIf="_pCols?.includes('allocation_daily')">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  formControlName="daily_billable_hours"
                  class="form-control-custom p-column-filter"
                  style="width: 4rem !important"
                  (input)="updateWeeklyBillableHours($event.target.value)"
                />
                <app-form-error class="position-rel" [validation]="'max'" [form]="addPositionForm" [controlName]="'daily_billable_hours'" [fieldLabel]="'It'"></app-form-error>
                <small *ngIf="addPositionForm.controls['daily_billable_hours'].hasError('min')" class="form-text text-danger"> It can have min value of 0 </small>
              </th>
              <th id="allocationWeekly" class="background-white" *ngIf="_pCols?.includes('allocation_weekly')">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  [value]="weeklyHours"
                  (input)="updateDailyBillableHours($event.target.value)"
                  class="form-control-custom p-column-filter"
                  style="width: 4rem !important"
                />
              </th>
              <th id="addCost" class="background-white">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  formControlName="cost"
                  class="form-control-custom p-column-filter"
                  style="width: 5rem !important"
                  placeholder="Cost"
                />
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'cost'" [fieldLabel]="'Cost'"></app-form-error>
              </th>
              <th id="addExpense" class="background-white"></th>
              <th id="addBillRate" *ngIf="showBillRate && _pCols?.includes('Bill Rate')" class="background-white">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  type="number"
                  formControlName="bill_rate"
                  class="form-control-custom p-column-filter"
                  style="width: 6rem !important"
                  placeholder="Bill Rate"
                />
                <app-form-error class="position-rel" [validation]="'required'" [form]="addPositionForm" [controlName]="'bill_rate'" [fieldLabel]="'Bill Rate'"></app-form-error>
              </th>
              <th *ngIf="showBillRate && _pCols?.includes('Margin')" class="background-white"></th>

              <th class="background-white">
                <p-dropdown
                  [options]="purchaseOrderList"
                  optionValue="id"
                  formControlName="purchase_order_id"
                  styleClass="p-column-filter pi-icon form-control-custom purchase-order"
                  placeholder="PO Number"
                  [showClear]="true"
                  appendTo="body"
                >
                </p-dropdown>
                <app-form-error
                  class="position-rel"
                  [validation]="'required'"
                  [form]="addPositionForm"
                  [controlName]="'purchase_order_id'"
                  [fieldLabel]="'PO Number'"
                ></app-form-error>
              </th>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component == componentType">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <th class="background-white no-wrap" colspan="1" *ngIf="checkSelectedColumn(filedDetails?.name)"></th>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <th id="addNewPosition" class="text-center background-white">
                <div class="d-flex justify-content-center w-100">
                  <a href="javascript:;void" class="btn btn-hover-primary btn-icon btn-light btn-sm form-group" (click)="addNewPosition()">
                    <span title="Save Position" [inlineSVG]="'assets/media/svg/icons/color-save.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                  </a>
                  <a href="javascript:;void" class="btn btn-hover-primary btn-icon btn-light btn-sm form-group ml-2" (click)="resetFilter()">
                    <span title="Delete Position" [inlineSVG]="'assets/media/svg/icons/color-delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                  </a>
                </div>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-position let-editing="editing" let-ri="rowIndex">
            <tr [pEditableRow]="position" [ngClass]="{ 'billRate': showBillRate, 'editing-row': editing }">
              <!-- <td class="body-width-plus"></td> -->
              <td class="table-checkbox">
                <p-tableCheckbox [value]="position"></p-tableCheckbox>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-name">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      type="text"
                      class="editRow"
                      [(ngModel)]="editPositionObj.position.name"
                      appSflAutofocus
                      [ngModelOptions]="{ standalone: true }"
                      (input)="positionValueChange($event.target.value, position?.position?.id)"
                    />
                    <small class="form-text text-danger">
                      {{ showPositionError }}
                    </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" [title]="position?.position?.name" pInitEditableRow (click)="editPosition(position)" class="pointer">
                      {{ position?.position?.name }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" [title]="position?.position?.name" class="disabled-edit">
                      {{ position?.position?.name }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="body-person">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-dropdown
                      [options]="employeeList"
                      class="editRow"
                      [style]="{ width: '100%' }"
                      [(ngModel)]="editPositionObj.position.employee.id"
                      placeholder="Assign to"
                      appendTo="body"
                      [ngModelOptions]="{ standalone: true }"
                      (onChange)="employeeSelected($event, true)"
                    ></p-dropdown>
                    <small class="form-text text-danger" *ngIf="isShowEmployeeError.isErrorInEdit">
                      {{ appConstants.employeeErrorMessage }}
                    </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span
                      *ngIf="canEditRow(ri)"
                      pInitEditableRow
                      (click)="editPosition(position)"
                      class="pointer"
                      [title]="position?.position?.employee?.first_name ? position?.position?.employee?.first_name + ' ' + position?.position?.employee?.last_name : '--Open--'"
                    >
                      {{ position?.position?.employee?.first_name ? position?.position?.employee?.first_name + ' ' + position?.position?.employee?.last_name : '--Open--' }}
                    </span>
                    <span
                      *ngIf="!canEditRow(ri)"
                      class="disabled-edit"
                      [title]="position?.position?.employee?.first_name ? position?.position?.employee?.first_name + ' ' + position?.position?.employee?.last_name : '--Open--'"
                    >
                      {{ position?.position?.employee?.first_name ? position?.position?.employee?.first_name + ' ' + position?.position?.employee?.last_name : '--Open--' }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="body-skill">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-dropdown
                      [options]="skillSetList"
                      class="editRow"
                      [style]="{ width: '100%' }"
                      [(ngModel)]="editPositionObj.position.position_type_id"
                      placeholder="Skill Set"
                      appendTo="body"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-dropdown>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)" [title]="position?.position?.type">
                      {{ position?.position?.type }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit" [title]="position?.position?.type">
                      {{ position?.position?.type }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-start">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-calendar
                      appendTo="body"
                      (onSelect)="editPositionStartDateSelected()"
                      [readonlyInput]="false"
                      inputId="startDate"
                      [(ngModel)]="editPositionObj.position.start_date"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-calendar>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)">
                      {{ position?.position?.start_date | date : 'MM/dd/yyyy' }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit">
                      {{ position?.position?.start_date | date : 'MM/dd/yyyy' }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-end" (clickOutside)="position.showHelpIconData = false" style="position: relative">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-calendar
                      appendTo="body"
                      [minDate]="positionMinDate"
                      (onSelect)="editPositionEndDateSelected()"
                      [readonlyInput]="false"
                      inputId="endDate"
                      [(ngModel)]="editPositionObj.position.end_date"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-calendar>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)">
                      {{ position?.position?.end_date | date : 'MM/dd/yyyy' }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit">
                      {{ position?.position?.end_date | date : 'MM/dd/yyyy' }}
                    </span>
                    <ng-template #popOver>
                      <div class="heading">DURATION</div>
                      <div class="sub-heading">
                        {{ getNoOfWeeks(position?.position) }}
                      </div>
                      <div></div>
                    </ng-template>
                    <a placement="right" container="body" (click)="showHelpData(position)" [ngbPopover]="popOver">
                      <fa-icon [icon]="'info-circle'" class="help-icon ml-1"></fa-icon>
                    </a>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="body-allocation" *ngIf="_pCols?.includes('allocation_daily')">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      sflIsDecimalNumber
                      [decimals]="2"
                      pInputText
                      class="editRow"
                      type="number"
                      [(ngModel)]="editPositionObj.position.daily_billable_hours"
                      name="allocation"
                      (ngModelChange)="allocationValueChange()"
                      [ngModelOptions]="{ standalone: true }"
                    />
                    <small *ngIf="showAllocationError" class="form-text text-danger"> Value should be between 0 and 8 </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)"> {{ position?.position?.daily_billable_hours }}h </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit"> {{ position?.position?.daily_billable_hours }}h </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td pEditableColumn [pEditableColumnDisabled]="true" class="body-allocation" *ngIf="_pCols?.includes('allocation_weekly')">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      pInputText
                      class="editRow"
                      type="number"
                      [(ngModel)]="editPositionObj.position.weekly_billable_hours"
                      [title]="returnExceptedValue(editPositionObj)"
                      [value]="returnExceptedValue(editPositionObj)"
                      name="allocation"
                      (ngModelChange)="editValueChange(editPositionObj)"
                      [ngModelOptions]="{ standalone: true }"
                    />
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)">
                      {{ (position?.position?.daily_billable_hours * 5).toFixed(2) }}h
                    </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit"> {{ (position?.position?.daily_billable_hours * 5).toFixed(2) }}h </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="_pCols?.includes('cost')" pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-cost">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-inputNumber
                      [minFractionDigits]="2"
                      class="form-control amount-form-control"
                      (onKeyDown)="costValueChange($event)"
                      class="editRow"
                      [(ngModel)]="editPositionObj.position.cost"
                      [ngModelOptions]="{ standalone: true }"
                    ></p-inputNumber>
                    <small *ngIf="showCostError" class="form-text text-danger"> Cost is required </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)"> ${{ position?.position?.cost || 0.0 }} </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit"> ${{ position?.position?.cost || 0.0 }} </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <ng-container *ngIf="_pCols?.includes('expense')">
                <ng-container *ngIf="(projectService.recalculatingTheRevenue$ | async) && checkIfThisExpenseIsBeingUpdating(position); else showExpense">
                  <td>
                    <div class="pulse-container">
                      <div class="dot-pulse"></div>
                    </div>
                  </td>
                </ng-container>
              </ng-container>

              <ng-template #showExpense>
                <td class="body-expense show-pointer" (click)="showAddExpenseModal(position)">
                  <div>
                    <span *ngIf="currentProjectState === this.currentProjectStateValue.Paused && isPositionInChangedList(position)"> --- </span>
                    <span *ngIf="this.financialCalculation && isPositionInChangedList(position)">
                      <ng-container *ngTemplateOutlet="InProgressCell"></ng-container>
                    </span>
                    <span *ngIf="!isPositionInChangedList(position)"> {{ '$' }} {{ position.position.expense | addCommasToNumbers }} </span>
                  </div>
                </td>
              </ng-template>
              <td *ngIf="showBillRate && _pCols?.includes('Bill Rate')" pEditableColumn [pEditableColumnDisabled]="true" class="position-abs body-rate">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <input
                      sflIsDecimalNumber
                      [decimals]="2"
                      pInputText
                      type="text"
                      class="editRow"
                      [(ngModel)]="editPositionObj.position.bill_rate"
                      [ngModelOptions]="{ standalone: true }"
                      (input)="billRateValueChange()"
                    />
                    <small *ngIf="showBillRateError" class="form-text text-danger"> Bill Rate is required </small>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)">
                      {{ position?.position?.bill_rate ? '$' + position?.position?.bill_rate : '' }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" class="disabled-edit">
                      {{ position?.position?.bill_rate ? '$' + position?.position?.bill_rate : '' }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>
              <td *ngIf="showBillRate && _pCols?.includes('Margin')" class="body-margin">
                <span *ngIf="canEditRow(ri)" class="pointer" pInitEditableRow (click)="editPosition(position)">
                  {{ position?.position?.margin ? (position?.position?.margin | addCommasToNumbers) + '%' : '0%' }}
                </span>
                <span *ngIf="!canEditRow(ri)" class="disabled-edit">
                  {{ position?.position?.margin ? (position?.position?.margin | addCommasToNumbers) + '%' : '0%' }}
                </span>
              </td>

              <td pEditableColumn [pEditableColumnDisabled]="true" class="body-person" *ngIf="_pCols?.includes('purchase_order')">
                <p-cellEditor>
                  <ng-template pTemplate="input">
                    <p-dropdown
                      [options]="purchaseOrderList"
                      optionValue="id"
                      [(ngModel)]="editPositionObj.position.purchase_order_id"
                      styleClass="p-column-filter pi-icon form-control-custom purchase-order"
                      placeholder="PO Number"
                      appendTo="body"
                      [showClear]="true"
                      [ngModelOptions]="{ standalone: true }"
                    >
                    </p-dropdown>
                  </ng-template>
                  <ng-template pTemplate="output">
                    <span *ngIf="canEditRow(ri)" id="purchase order test" class="pointer" pInitEditableRow (click)="editPosition(position)">
                      {{ position?.position?.purchase_order?.po_number || '--' }}
                    </span>
                    <span *ngIf="!canEditRow(ri)" id="purchase order test" class="disabled-edit">
                      {{ position?.position?.purchase_order?.po_number || '--' }}
                    </span>
                  </ng-template>
                </p-cellEditor>
              </td>

              <ng-container *ngFor="let item of extendFields; let index = index">
                <ng-container *ngFor="let filed of item?.jsonData?.extendedFieldsConfig; let index2 = index">
                  <ng-container *ngIf="filed?.component == componentType">
                    <ng-container *ngFor="let filedDetails of filed?.fields; let index3 = index">
                      <td
                        class="header-price show-pointer"
                        colspan="1"
                        *ngIf="checkSelectedColumn(filedDetails?.name)"
                        [id]="filedDetails?.name"
                        (click)="openExtendFiledPopup(position, filedDetails?.name)"
                        [ngClass]="{ 'dynamic-text text-truncate': filedDetails.type === filedType.Text_Area }"
                      >
                        {{ getValueByPartialKey(filedDetails?.DBTag, position?.position?.extended_fields) }}
                      </td>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </ng-container>

              <td class="text-center">
                <div ngbDropdown class="d-inline-block" *ngIf="!editing" container="body">
                  <button class="btn btn-clean btn-expand btn-icon btn-icon-md btn-sm" id="dropdownBasic1" ngbDropdownToggle [disabled]="positionEdit">
                    <em class="flaticon-more"></em>
                  </button>
                  <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left">
                    <button ngbDropdownItem (click)="showAddExpenseModal(position)">Add Expense</button>
                    <button #EditableColumn ngbDropdownItem pInitEditableRow (click)="editPosition(position)" [disabled]="!canEditRow(ri)">Edit</button>
                    <button ngbDropdownItem (click)="duplicatePosition(position, dt)">Duplicate</button>
                    <button ngbDropdownItem (click)="confirmDeleteProject(position?.position?.id)">Delete</button>
                    <button ngbDropdownItem (click)="AddTags(position?.position)">Tags</button>
                  </div>
                </div>
                <button
                  *ngIf="editing"
                  pButton
                  pRipple
                  type="button"
                  icon="pi pi-check"
                  title="I am hear"
                  class="p-button-rounded p-button-success p-button-text p-mr-2"
                  (click)="handleSaveOrAddNewPosition(position.position.position, dt)"
                ></button>
                <button
                  *ngIf="editing"
                  pButton
                  pRipple
                  type="button"
                  pCancelEditableRow
                  icon="pi pi-times"
                  class="p-button-danger p-button-rounded p-button-text"
                  (click)="handleCancelOrDeleteDuplicatedPosition(position, ri)"
                ></button>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="12" class="center-align">{{ loadingMsg }}</td>
            </tr>
          </ng-template>
        </p-table>
      </form>
    </div>
    <div class="d-flex btn-fix justify-content-between fixed-content px-5 py-5">
      <div type="button" class="btn btn-height btn-prev text-uppercase font-weight-bold px-9 py-4" (click)="prevStep()">Prev</div>
      <div type="submit" class="btn btn-height btn-primary text-uppercase font-weight-bold px-9 py-4" [isSubmitting]="isSubmitting" (click)="nextStep()">Next</div>
    </div>
  </div>
</form>

<p-dialog
  (onHide)="closeModal()"
  header="Expense"
  [(visible)]="showExpenseModal"
  [modal]="true"
  class="expense-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [autoZIndex]="true"
  [resizable]="false"
  [style]="{ zIndex: 10000 }"
  appendTo="body"
>
  <form class="p-m-0 mb-2 mt-2" [formGroup]="addExpenseForm" autocomplete="off" novalidate="novalidate" id="add_expense_form">
    <p-table
      #dt1
      [value]="positionExpenseList"
      dataKey="id"
      [loading]="expenseLoader"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [style]="{ overflow: 'auto!important' }"
      editMode="row"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="type" class="header-width">Frequency</th>
          <th id="cost" class="header-width">Cost</th>
          <th id="description" class="header-width">Description</th>
          <th id="expenseType" class="header-width">Expense Type</th>
          <th id="month" class="month-section">Month/Year</th>
          <th id="actions" class="action-width"></th>
        </tr>
        <tr>
          <th id="addType">
            <p-dropdown
              appendTo="body"
              [options]="expenses"
              formControlName="type"
              styleClass="p-column-filter pi-icon form-control-custom"
              placeholder="Select Frequency"
              (onChange)="expenseSelected($event)"
            >
            </p-dropdown>
          </th>
          <th id="addCost">
            <ng-container *ngIf="allowExpenseSelection">
              <input sflIsDecimalNumber [decimals]="2" pInputText type="search" class="form-control-custom" formControlName="cost" placeholder="0.00" />
              <app-form-error class="position-abs" [validation]="'required'" [form]="addExpenseForm" [controlName]="'cost'" [fieldLabel]="'Amount'"></app-form-error>
            </ng-container>
          </th>
          <th id="addDescription">
            <ng-container *ngIf="allowExpenseSelection">
              <input pInputText type="search" class="form-control-custom" formControlName="description" placeholder="Description" />
              <app-form-error class="position-abs" [validation]="'required'" [form]="addExpenseForm" [controlName]="'description'" [fieldLabel]="'Description'"></app-form-error>
            </ng-container>
          </th>
          <th id="addExpenseType">
            <ng-container *ngIf="allowExpenseSelection">
              <p-dropdown
                appendTo="body"
                [options]="showDailyExpenseType ? dailyExpenseTypes : monthlyExpenseTypes"
                formControlName="type_id"
                styleClass="p-column-filter pi-icon form-control-custom"
                placeholder="Expense Type"
              >
              </p-dropdown>
              <app-form-error class="position-abs" [validation]="'required'" [form]="addExpenseForm" [controlName]="'type_id'" [fieldLabel]="'Expense Type'"></app-form-error>
            </ng-container>
          </th>
          <th id="addMonth" class="calender-month">
            <ng-container *ngIf="allowExpenseSelection && !showDailyExpenseType" class="form-group">
              <div class="position-relative month-calendar-wrapper">
                <div class="selected-months-display">
                  <ng-container *ngIf="selectedMonths?.length > 0">
                    <p-chip
                      *ngFor="let date of selectedMonths"
                      [label]="getFormattedMonthYear(date)"
                      [removable]="true"
                      (onRemove)="removeSelectedMonth(date)"
                      styleClass="month-chip"
                      [attr.style]="'z-index: 3'"
                    ></p-chip>
                  </ng-container>
                </div>

                <div id="custom-month-edit" class="custom-month-selection fix-calender-pos">
                  <p-calendar
                    #customMonthCalender
                    id="custom-month-calender"
                    [maxDate]="maxDate"
                    [minDate]="minDate"
                    [(ngModel)]="selectedMonths"
                    [ngModelOptions]="{ standalone: true }"
                    selectionMode="multiple"
                    view="month"
                    dateFormat="M/y"
                    [showIcon]="true"
                    [yearNavigator]="true"
                    [monthNavigator]="true"
                    [readonlyInput]="true"
                    [style]="{ width: '100%' }"
                    appendTo="body"
                    [showClear]="true"
                    showButtonBar="true"
                    placeholder="Month/Year"
                    (onShow)="highlightSelectedMonths(); hideCalendarNavButtons()"
                    (ngModelChange)="highlightSelectedMonths($event); onMonthSelect()"
                    (onViewChange)="highlightSelectedMonths()"
                    (onClearClick)="highlightSelectedMonths()"
                    (onYearChange)="highlightSelectedMonths()"
                  ></p-calendar>
                </div>
              </div>
              <small class="text-danger" *ngIf="!selectedMonths?.length">Please select at least one month.</small>
            </ng-container>
          </th>

          <th class="d-flex" id="addNewExpense">
            <ng-container *ngIf="allowExpenseSelection">
              <div class="d-flex flex-nowrap flex-row">
                <a href="javascript:;void" class="btn btn-icon btn-light btn-plus btn-sm" (click)="addExpense()">
                  <span title="Add Expense" [inlineSVG]="'assets/media/svg/icons/plus.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                </a>
                <a title="Reset Filter" (click)="resetFilter()" class="btn btn-icon btn-icon-light btn-light btn-sm icon-background">
                  <span title="Reset Filter" [inlineSVG]="imageConst.purchaseOrderIcon" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
                </a>
              </div>
            </ng-container>
          </th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-expense let-editing="editing" let-ri="rowIndex">
        <tr [pEditableRow]="expense">
          <td>
            {{ expense?.key }}
          </td>
          <td pEditableColumn [pEditableColumnDisabled]="true">
            <p-cellEditor>
              <ng-template pTemplate="input">
                <input
                  sflIsDecimalNumber
                  [decimals]="2"
                  pInputText
                  class="editRow"
                  type="search"
                  (input)="expenseCostChange()"
                  [(ngModel)]="editExpenseObject.cost"
                  name="cost"
                  [ngModelOptions]="{ standalone: true }"
                />
                <small *ngIf="showCostError" class="form-text text-danger"> Cost is required </small>
              </ng-template>
              <ng-template pTemplate="output"> ${{ expense?.cost }} </ng-template>
            </p-cellEditor>
          </td>
          <td pEditableColumn [pEditableColumnDisabled]="true">
            <p-cellEditor>
              <ng-template pTemplate="input">
                <input
                  pInputText
                  type="search"
                  class="editRow"
                  (input)="expenseDescChange()"
                  [(ngModel)]="editExpenseObject.description"
                  name="desc"
                  [ngModelOptions]="{ standalone: true }"
                />
                <small *ngIf="showDescriptionError" class="form-text text-danger"> Description is required </small>
              </ng-template>
              <ng-template pTemplate="output">
                {{ expense?.description }}
              </ng-template>
            </p-cellEditor>
          </td>
          <td>
            <p-cellEditor>
              <ng-template pTemplate="input">
                <p-dropdown
                  appendTo="body"
                  [options]="expense?.key === 'Daily' ? dailyExpenseTypes : monthlyExpenseTypes"
                  class="editRow"
                  [style]="{ width: '100%' }"
                  [(ngModel)]="editExpenseObject.type_id"
                  [ngModelOptions]="{ standalone: true }"
                >
                </p-dropdown>
              </ng-template>
              <ng-template pTemplate="output">
                {{ expense?.key === 'Daily' ? expense?.type?.name : expense?.expense_type?.name }}
              </ng-template>
            </p-cellEditor>
          </td>
          <td pEditableColumn [pEditableColumnDisabled]="true">
            <p-cellEditor>
              <ng-template pTemplate="input" *ngIf="expense?.month">
                <p-calendar
                  class="edit-cal"
                  appendTo="body"
                  view="month"
                  [(ngModel)]="editExpenseObject.date"
                  [maxDate]="maxDate"
                  [minDate]="minDate"
                  dateFormat="M/y"
                  [yearNavigator]="true"
                  [yearRange]="yearRange"
                  [readonlyInput]="true"
                  inputId="monthpicker"
                  placeholder="Month/Year"
                  [ngModelOptions]="{ standalone: true }"
                >
                </p-calendar>
              </ng-template>
              <ng-template pTemplate="output">
                {{ expense?.month ? this.getMonthName(expense.month) + ' ' + expense?.year : ('' | uppercase) }}
              </ng-template>
            </p-cellEditor>
          </td>
          <td>
            <div ngbDropdown container="dt1" class="d-inline-block" *ngIf="!editing">
              <button class="btn btn-clean btn-expand btn-icon btn-icon-md btn-sm" id="dropdownBasic1" ngbDropdownToggle>
                <em class="flaticon-more"></em>
              </button>
              <div ngbDropdownMenu aria-labelledby="dropdownBasic1" isOpen="false" placement="left" [style]="{ zIndex: 99999 }">
                <button ngbDropdownItem pInitEditableRow (click)="editExpense(expense, ri)">Edit</button>
                <button ngbDropdownItem (click)="confirmDeleteExpense(expense)">Delete</button>
              </div>
            </div>
            <button
              *ngIf="editing"
              pButton
              pRipple
              type="button"
              icon="pi pi-check"
              class="p-button-rounded p-button-success p-button-text p-mr-2"
              (click)="saveEditExpense(ri)"
            ></button>
            <button
              *ngIf="editing"
              pCancelEditableRow
              pButton
              pRipple
              type="button"
              icon="pi pi-times"
              class="p-button-danger p-button-rounded p-button-text"
              (click)="cancelEditExpense(expense, ri)"
            ></button>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="12" class="center-align">No Expense found.</td>
        </tr>
      </ng-template>
    </p-table>
  </form>
  <ng-template pTemplate="footer"> </ng-template>
</p-dialog>

<p-dialog header="Delete Position" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure that you want to delete?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap align-items-center justify-content-end">
      <button type="button" class="btn-cancel" (click)="closeDeleteModal()">No</button>
      <button type="button" class="btn-save" (click)="deletePosition()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete Expense" [(visible)]="showExpenseDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure that you want to delete?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap align-items-center justify-content-end">
      <button type="button" class="btn-cancel" (click)="closeDeleteModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteExpense()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  header="Update Position"
  [(visible)]="showPositionUpdateDateDialog"
  [modal]="true"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '20vw' }"
>
  <div class="d-flex flex-column date-calender">
    <form [formGroup]="updatePositionDate" (ngSubmit)="updatePostionDate()">
      <div class="my-3">
        <div class="position-date w-100 mb-1 me-1">
          <div class="d-flex mb-2">
            <p-checkbox name="positionStartDate" [binary]="true" formControlName="isUpdateStartDate"></p-checkbox>
            <p class="d-block mb-0 ml-1" [ngClass]="{ required: updatePositionDate?.value?.isUpdateStartDate }">Start Date</p>
          </div>
          <p-calendar
            *ngIf="updatePositionDate?.value?.isUpdateStartDate"
            appendTo="body"
            [readonlyInput]="false"
            inputId="startDate"
            formControlName="start_date"
            placeholder="Start Date"
            showButtonBar="true"
            [showIcon]="true"
            (onClose)="onDateChange()"
          >
          </p-calendar>
        </div>
        <app-form-error class="position-rel" [validation]="'required'" [form]="updatePositionDate" [controlName]="'start_date'" [fieldLabel]="'Start Date'"></app-form-error>
        <small *ngIf="!isValidSelectedDate" class="form-text text-danger mb-2 mt-1"> Start date must be before end date. </small>
      </div>
      <div>
        <div class="position-date w-100 me-1">
          <div class="d-flex mb-2">
            <p-checkbox name="positionEndDate" [binary]="true" formControlName="isUpdateEndDate"></p-checkbox>
            <p class="d-block mb-0 ml-1" [ngClass]="{ required: updatePositionDate?.value?.isUpdateEndDate }">End date</p>
          </div>
          <p-calendar
            *ngIf="updatePositionDate?.value?.isUpdateEndDate"
            [readonlyInput]="false"
            appendTo="body"
            inputId="endDate"
            formControlName="end_date"
            placeholder="End Date"
            showButtonBar="true"
            [showIcon]="true"
            [minDate]="minDateOfProject"
            (onClose)="onDateChange()"
          >
          </p-calendar>
        </div>
        <app-form-error class="position-rel" [validation]="'required'" [form]="updatePositionDate" [controlName]="'end_date'" [fieldLabel]="'End Date'"></app-form-error>
      </div>
    </form>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap align-items-center justify-content-end mt-2">
      <button id="addContactCancel" type="button" class="btn-cancel" (click)="onCancelEditDate()">Cancel</button>
      <button [disabled]="isSaveButtonDisable()" id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" (click)="updatePostionDate()" class="btn-save">Save</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [header]="'Update ' + updateExtendFiled"
  [(visible)]="showUpdateExtendFiledDialog"
  [modal]="true"
  class="description-dialog"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [style]="{ width: '40vw' }"
>
  <app-extended-form [extendFieldsObj]="positionObj?.position?.extended_fields" [filedName]="updateExtendFiled" [componentType]="componentType"> </app-extended-form>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap align-items-center justify-content-end">
      <button type="button" class="btn-cancel" (click)="closeExtendFiledPopup()">Cancel</button>
      <button type="button" class="btn-save" (click)="saveEditPosition(true)" [isSubmitting]="isSubmitting">Save</button>
    </div>
  </ng-template>
</p-dialog>

<p-confirmDialog key="conformationDialog" [position]="'center'" rejectButtonStyleClass="p-button-outlined"></p-confirmDialog>

<p-dialog
  header="Different Cost"
  [(visible)]="showCostDifferentDialog"
  class="confirm-dialog"
  [modal]="true"
  [baseZIndex]="5000"
  [draggable]="false"
  [resizable]="false"
  [closable]="false"
>
  <div class="cost-different-dialo-text d-flex align-center">
    <em class="pi pi-exclamation-triangle cost-dialog-warning"></em>
    <span class="p-m-0">Cost are different on start & end date, You may want to create two position.</span>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" [isSubmitting]="isSubmitting" (click)="onCloseCostDifferentDialog()">Ok</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Purchase Order" [(visible)]="showPOUpdateDialog" [modal]="true" [baseZIndex]="10000" [draggable]="false" [resizable]="false" [style]="{ width: '20vw' }">
  <div class="d-flex flex-column">
    <div class="pe-2">Select Purchase Order</div>
    <p-dropdown
      [options]="purchaseOrderList"
      optionValue="id"
      [(ngModel)]="purchaseOrderIdForMultiPos"
      styleClass="p-column-filter pi-icon form-control-custom po-dialog"
      placeholder="PO Number"
      appendTo="body"
      [ngModelOptions]="{ standalone: true }"
      [showClear]="true"
    >
    </p-dropdown>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center mt-2">
      <button id="addContactCancel" type="button" class="btn-cancel" (click)="onCancelUpdatePoForMultiPos()">Cancel</button>
      <button id="addContactSubmit" type="submit" [isSubmitting]="isSubmitting" (click)="updatePOForMultiPositions()" class="btn-save">Save</button>
    </div>
  </ng-template>
</p-dialog>

<ng-template #InProgressCell>
  <td>
    <div class="pulse-container">
      <div class="dot-pulse"></div>
    </div>
  </td>
</ng-template>

<!-- Pop-up dialog which show when selected date range is invalid for selected employee -->
<p-dialog
  header="Invalid Start or End Date"
  [(visible)]="showInvalidEmpDialog"
  class="confirm-dialog"
  [modal]="true"
  [baseZIndex]="5000"
  [draggable]="false"
  [resizable]="false"
  [closable]="false"
>
  <div class="cost-different-dialo-text d-flex align-center">
    <em class="pi pi-exclamation-triangle cost-dialog-warning"></em>
    <span class="p-m-0">Selected employee is not available on selected date range. Please select valid date range.</span>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" [isSubmitting]="isSubmitting" (click)="onCloseInvalidEmpDialog()">Ok</button>
    </div>
  </ng-template>
</p-dialog>
