<mat-sidenav-container>
  <mat-sidenav class="detail-sidebar" #sidebarFilter mode="over" position="end" disableClose>
    <app-apply-compare-filter
      [(filterData)]="dataFilter"
      (closeSidebarEvent)="onCloseSideBar($event)"
      [clientGroups]="clientGroups"
      [projectGroups]="projectGroups"
      [client]="client"
      [sidebarIcons]="sidebarButtons"
      (getProjectsIds)="getProjectsIds()"
      (getClientsIds)="getClientsIds()"
      [project]="projects"
      [statuses]="statuses"
      [defaultSelectedStatuses]="defaultStatuses"
      [minEffectiveDate]="minEffectiveDate"
      [tags]="groupedCategory?.data"
      [savedSelectedTags]="selectedTags"
    ></app-apply-compare-filter>
  </mat-sidenav>
  <mat-sidenav-content class="detail-sidebar-content">
    <div class="card card-custom gutter-b" id="comparePL">
      <app-card-header
        [cardTitle]="cardTitle"
        [cardSubTitle]="cardSubTitle"
        [showSplitButton]="true"
        [splitButtonDropDownOption]="splitButtonDropDownOption"
        class="card-header-wrapper"
      ></app-card-header>
      <app-selected-filter-tags
        [tags]="tags"
        (filterReset)="resetFilter()"
        (saveFilter)="onSaveFilter()"
        (onCancel)="onCancelFilter($event)"
        (onRemoveStatus)="onRemoveStatusFilter($event)"
      ></app-selected-filter-tags>
      <div class="card-body">
        <div #wizard1 class="wizard wizard-4" id="kt_wizard_v4" data-wizard-state="step-first">
          <!--begin: Form Wizard Nav -->
          <div class="wizard-nav">
            <div class="wizard-steps">
              <a class="wizard-step" data-wizard-type="step" data-wizard-state="current" data-wizard-clickable="true">
                <div class="wizard-wrapper">
                  <div class="wizard-number"></div>
                  <div class="wizard-label">
                    <div class="wizard-title">P&L Comparison</div>
                  </div>
                </div>
              </a>
              <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true">
                <div class="wizard-wrapper">
                  <div class="wizard-number"></div>
                  <div class="wizard-label">
                    <div class="wizard-title">Previous P&L</div>
                    <div class="wizard-desc" [title]="effectiveDate1">
                      {{ this.dataFilter?.effective_date1 | date : 'MM/dd/yyyy hh:mm' }}
                    </div>
                  </div>
                </div>
              </a>
              <a class="wizard-step" data-wizard-type="step" data-wizard-clickable="true">
                <div class="wizard-wrapper">
                  <div class="wizard-number"></div>
                  <div class="wizard-label">
                    <div class="wizard-title">Baseline P&L</div>
                    <div class="wizard-desc" [title]="effectiveDate2">
                      {{ (this.dataFilter?.effective_date2 | date : 'MM/dd/yyyy hh:mm') || currentDate }}
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>

          <div class="card card-custom card-shadowless rounded-top-0 cal-height">
            <div class="card-body p-0">
              <div class="row justify-content-center full-height">
                <div class="col-xl-12">
                  <!--begin: Form Wizard Form-->
                  <form class="form mt-0" id="kt-form">
                    <div data-wizard-type="step-content" data-wizard-state="current">
                      <div class="card-body pt-1">
                        <ng-container *ngIf="!finalProjectionData?.length && !(loading$ | async)">
                          <p class="filter-note">Please apply filter to load the P&L Comparision.</p>
                        </ng-container>
                        <div *ngIf="!resizeFlag; else resize_table">
                          <p-treeTable
                            [value]="finalProjectionData"
                            *isFetchingData="loading$"
                            frozenWidth="390px"
                            [frozenColumns]="frozenCols"
                            [scrollable]="true"
                            [columns]="tableHeaders"
                            (onNodeExpand)="makeRowsSameHeight()"
                            (onNodeCollapse)="makeRowsSameHeight()"
                          >
                            <ng-template pTemplate="colgroup" let-columns>
                              <colgroup>
                                <col *ngFor="let col of columns" style="width: 120px" />
                                <col style="width: 120px" />
                              </colgroup>
                            </ng-template>
                            <ng-template pTemplate="header" let-columns>
                              <tr>
                                <th *ngFor="let col of columns">
                                  {{ col.monthLabel }}
                                </th>
                                <th class="p-r-15">Total</th>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder">
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'SG&A' || rowNode?.node?.data?.type === 'Revenue Adjustment' || rowNode?.node?.data?.type === 'COGS Adjustment';
                                      else monthlyValues
                                    "
                                  >
                                    <ng-container *ngIf="rowNode?.node?.data?.type !== 'SG&A'; else sgaPermissionBlock">
                                      <td>
                                        <div
                                          class="d-block cursor-pointer"
                                          [pTooltip]="showToolTip(monthlyData, rowNode, 'projection1')"
                                          tooltipPosition="right"
                                          appendTo="body"
                                          [escape]="false"
                                        >
                                          <div>
                                            {{ '$' + (monthlyData?.value?.effective_date_1 | addCommasToNumbers) }}
                                          </div>
                                          <span *ngIf="monthlyData?.value?.diff == 0; else nonZeroValues">
                                            {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                          </span>
                                          <ng-template #nonZeroValues>
                                            <span
                                              [ngClass]="{
                                                green: getSymbol(monthlyData?.value?.diff, rowNode),
                                                red: !getSymbol(monthlyData?.value?.diff, rowNode)
                                              }"
                                            >
                                              {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                              <span *ngIf="getSymbol(monthlyData?.value?.diff, rowNode); else downSymbol" class="green">
                                                <em class="fa-solid fa-sort-up"></em>
                                              </span>
                                              <ng-template #downSymbol>
                                                <span class="red">
                                                  <em class="fa-solid fa-sort-down"></em>
                                                </span>
                                              </ng-template>
                                            </span>
                                          </ng-template>
                                        </div>
                                      </td>
                                    </ng-container>
                                    <ng-template #sgaPermissionBlock>
                                      <td *ngIf="isAllowedNetMargin">
                                        <div
                                          class="d-block cursor-pointer"
                                          [pTooltip]="showToolTip(monthlyData, rowNode, 'projection1')"
                                          tooltipPosition="right"
                                          appendTo="body"
                                          [escape]="false"
                                        >
                                          <div>
                                            {{ '$' + (monthlyData?.value?.effective_date_1 | addCommasToNumbers) }}
                                          </div>
                                          <span *ngIf="monthlyData?.value?.diff == 0; else nonZeroValues">
                                            {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                          </span>
                                          <ng-template #nonZeroValues>
                                            <span
                                              [ngClass]="{
                                                green: getSymbol(monthlyData?.value?.diff, rowNode),
                                                red: !getSymbol(monthlyData?.value?.diff, rowNode)
                                              }"
                                            >
                                              {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                              <span *ngIf="getSymbol(monthlyData?.value?.diff, rowNode); else downSymbol" class="green">
                                                <em class="fa-solid fa-sort-up"></em>
                                              </span>
                                              <ng-template #downSymbol>
                                                <span class="red">
                                                  <em class="fa-solid fa-sort-down"></em>
                                                </span>
                                              </ng-template>
                                            </span>
                                          </ng-template>
                                        </div>
                                      </td>
                                    </ng-template>
                                  </ng-container>
                                  <ng-template #monthlyValues>
                                    <ng-container
                                      *ngIf="rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit'; else monthlyValueExceptNetMargin"
                                    >
                                      <ng-container *ngIf="isAllowedNetMargin">
                                        <td>
                                          <ng-template
                                            [ngTemplateOutlet]="colValueExceptNetMargin"
                                            [ngTemplateOutletContext]="{ rowNode: rowNode, rowData: rowData, monthlyData: monthlyData }"
                                          ></ng-template>
                                        </td>
                                      </ng-container>
                                    </ng-container>
                                    <ng-template #monthlyValueExceptNetMargin>
                                      <td>
                                        <ng-template
                                          [ngTemplateOutlet]="colValueExceptNetMargin"
                                          [ngTemplateOutletContext]="{ rowNode: rowNode, rowData: rowData, monthlyData: monthlyData }"
                                        ></ng-template>
                                      </td>
                                    </ng-template>
                                  </ng-template>
                                </ng-container>
                                <ng-container>
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                      else monthlyValueExceptNetMargin
                                    "
                                  >
                                    <td class="font-weight-bold p-r-15" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 0) | addCommasToNumbers) + '%'
                                          : '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                      }}
                                    </td>
                                  </ng-container>
                                  <ng-template #monthlyValueExceptNetMargin>
                                    <td class="font-weight-bold p-r-15">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 0) | addCommasToNumbers) + '%'
                                          : rowNode.parent.data.type !== 'Gross Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                      }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                    </td>
                                  </ng-template>
                                </ng-container>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="frozenbody" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container
                                  *ngIf="
                                    rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                    else nameAcceptingNetMargin
                                  "
                                >
                                  <td [ngClass]="getStyle(rowNode)" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                    <span [title]="rowData?.type"> {{ rowData?.type }} </span>
                                  </td>
                                  <td [title]="rowData?.name" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                    {{ rowData?.name }}
                                  </td>
                                </ng-container>
                                <ng-template #nameAcceptingNetMargin>
                                  <td [ngClass]="getStyle(rowNode)">
                                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                    <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                  </td>
                                  <td [title]="rowData?.name">
                                    {{ rowData?.name }}
                                  </td>
                                </ng-template>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                              <tr *ngIf="!loading$">
                                <td colspan="4" class="center-align">No Data found.</td>
                              </tr>
                            </ng-template>
                          </p-treeTable>
                        </div>
                        <ng-template #resize_table>
                          <div class="scrollable-content">
                            <p-treeTable
                              [value]="finalProjectionData"
                              *isFetchingData="loading$"
                              [scrollable]="true"
                              [scrollHeight]="height"
                              [columns]="tableHeaders"
                              (onNodeExpand)="makeRowsSameHeight()"
                              (onNodeCollapse)="makeRowsSameHeight()"
                            >
                              <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                  <col style="width: 100px" />
                                  <col style="width: 100px" />
                                  <col *ngFor="let col of columns" style="width: 120px" />
                                  <col style="width: 120px" />
                                </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header" let-columns>
                                <tr>
                                  <th></th>
                                  <th></th>
                                  <th *ngFor="let col of columns">
                                    {{ col.monthLabel }}
                                  </th>
                                  <th>Total</th>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                                <tr style="height: 57px">
                                  <td [ngClass]="getStyle(rowNode)">
                                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                    <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                  </td>
                                  <td [title]="rowData?.name">
                                    {{ rowData?.name }}
                                  </td>
                                  <td *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder">
                                    <div
                                      class="d-block cursor-pointer"
                                      [pTooltip]="showToolTip(monthlyData, rowNode, 'projection2')"
                                      tooltipPosition="right"
                                      appendTo="body"
                                      [escape]="false"
                                      *ngIf="
                                        rowNode?.node?.data?.type === 'SG&A' ||
                                          rowNode?.node?.data?.type === 'Revenue Adjustment' ||
                                          rowNode?.node?.data?.type === 'COGS Adjustment';
                                        else monthlyValues
                                      "
                                    >
                                      <div>
                                        {{ '$' + (monthlyData?.value?.effective_date_2 | addCommasToNumbers) }}
                                      </div>
                                      <span *ngIf="monthlyData?.value?.diff == 0; else nonZeroValues">
                                        {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                      </span>
                                      <ng-template #nonZeroValues>
                                        <span
                                          [ngClass]="{
                                            green: getSymbol(monthlyData?.value?.diff, rowNode),
                                            red: !getSymbol(monthlyData?.value?.diff, rowNode)
                                          }"
                                        >
                                          {{ '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
                                          <span *ngIf="getSymbol(monthlyData?.value?.diff, rowNode); else downSymbol" class="green">
                                            <em class="fa-solid fa-sort-up"></em>
                                          </span>
                                          <ng-template #downSymbol>
                                            <span class="red">
                                              <em class="fa-solid fa-sort-down"></em>
                                            </span>
                                          </ng-template>
                                        </span>
                                      </ng-template>
                                    </div>
                                    <ng-template #monthlyValues>
                                      <div
                                        class="d-block cursor-pointer"
                                        [pTooltip]="showToolTip(monthlyData, rowNode, 'projection2')"
                                        tooltipPosition="right"
                                        appendTo="body"
                                        [escape]="false"
                                      >
                                        <div>
                                          {{
                                            rowNode?.level === 0
                                              ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                ? '$' + (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
                                                : (monthlyData?.value?.effective_date_2 | addCommasToNumbers) + '%'
                                              : rowNode?.parent?.data?.type !== 'Gross Margin'
                                              ? '$' + (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
                                              : (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
                                          }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                        </div>
                                        <span *ngIf="monthlyData?.value?.diff == 0; else nonZeroMonthlyValues">
                                          {{
                                            rowNode?.parent?.data?.type === 'Gross Margin' || rowNode?.node?.data?.type === 'Gross Margin'
                                              ? '0%'
                                              : '$' + (monthlyData?.value?.diff | addCommasToNumbers)
                                          }}
                                        </span>
                                        <ng-template #nonZeroMonthlyValues>
                                          <span
                                            [ngClass]="{
                                              green: getSymbol(monthlyData?.value?.diff, rowNode),
                                              red: !getSymbol(monthlyData?.value?.diff, rowNode)
                                            }"
                                          >
                                            {{
                                              rowNode?.level === 0
                                                ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                  ? '$' + (monthlyData?.value?.diff | addCommasToNumbers)
                                                  : (monthlyData?.value?.diff | addCommasToNumbers) + '%'
                                                : rowNode?.parent?.data?.type !== 'Gross Margin'
                                                ? '$' + (monthlyData?.value?.diff | addCommasToNumbers)
                                                : (monthlyData?.value?.diff | addCommasToNumbers)
                                            }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                            <span *ngIf="getSymbol(monthlyData?.value?.diff, rowNode); else downSymbol" class="green">
                                              <!-- if the cost goes up that would not be good and will show red up arrow; vice-a-versa -->
                                              <ng-container
                                                *ngIf="
                                                  rowNode?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                    rowNode?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                    rowNode?.parent?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                    rowNode?.node?.data?.type === appConstants.costOfGoodsSold;
                                                  else upArrow
                                                "
                                              >
                                                <em class="fa-solid fa-sort-down"></em>
                                              </ng-container>
                                              <ng-template #upArrow>
                                                <em class="fa-solid fa-sort-up"></em>
                                              </ng-template>
                                            </span>
                                            <ng-template #downSymbol>
                                              <span class="red">
                                                <ng-container
                                                  *ngIf="
                                                    rowNode?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                      rowNode?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                      rowNode?.parent?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                                                      rowNode?.node?.data?.type === appConstants.costOfGoodsSold;
                                                    else downArrow
                                                  "
                                                >
                                                  <em class="fa-solid fa-sort-up"></em>
                                                </ng-container>
                                                <ng-template #downArrow>
                                                  <em class="fa-solid fa-sort-down"></em>
                                                </ng-template>
                                              </span>
                                            </ng-template>
                                          </span>
                                        </ng-template>
                                      </div>
                                    </ng-template>
                                  </td>
                                  <td class="font-weight-bold p-r-15">
                                    {{
                                      rowNode?.level === 0
                                        ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 0) | addCommasToNumbers) + '%'
                                        : rowNode.parent.data.type !==
                                          'Gross
                                  Margin'
                                        ? '$' + (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                        : (getTotal(rowData, rowNode, 0) | addCommasToNumbers)
                                    }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                  </td>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                <tr *ngIf="!loading$">
                                  <td colspan="4" class="center-align">No Data found.</td>
                                </tr>
                              </ng-template>
                            </p-treeTable>
                          </div>
                        </ng-template>
                      </div>
                    </div>
                    <div data-wizard-type="step-content">
                      <div class="card-body pt-1">
                        <ng-container *ngIf="!finalProjectionData?.length && !(loading$ | async)">
                          <p class="filter-note">Please apply filter to load the P&L Comparision.</p>
                        </ng-container>
                        <div *ngIf="!resizeFlag; else resize_table">
                          <p-treeTable
                            [value]="projectionData1"
                            *isFetchingData="loading$"
                            frozenWidth="390px"
                            [frozenColumns]="frozenCols"
                            [scrollable]="true"
                            [columns]="tableHeaders"
                            (onNodeExpand)="makeRowsSameHeight()"
                            (onNodeCollapse)="makeRowsSameHeight()"
                          >
                            <ng-template pTemplate="colgroup" let-columns>
                              <colgroup>
                                <col *ngFor="let col of columns" style="width: 120px" />
                                <col style="width: 120px" />
                              </colgroup>
                            </ng-template>
                            <ng-template pTemplate="header" let-columns>
                              <tr>
                                <th *ngFor="let col of columns">
                                  {{ col.monthLabel }}
                                </th>
                                <th class="p-r-15">Total</th>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder">
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'SG&A' || rowNode?.node?.data?.type === 'Revenue Adjustment' || rowNode?.node?.data?.type === 'COGS Adjustment';
                                      else monthlyValues
                                    "
                                  >
                                    <ng-container *ngIf="rowNode?.node?.data?.type !== 'SG&A'; else sgaPermissionBlock">
                                      <td>
                                        {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                      </td>
                                    </ng-container>

                                    <ng-template #sgaPermissionBlock>
                                      <ng-container *hasAnyPermission="permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                        <td>
                                          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                        </td>
                                      </ng-container>
                                    </ng-template>
                                  </ng-container>
                                  <ng-template #monthlyValues>
                                    <td *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'; else monthlyValueExceptPosition">
                                      <span>
                                        <span
                                          class=""
                                          *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'"
                                          [ngbTooltip]="tipContent"
                                          tooltipClass="negative-bench-tooltip"
                                          placement="auto"
                                          appendTo="body"
                                        >
                                          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                        </span>
                                        <ng-template #tipContent>
                                          <div
                                            *ngIf="getSeparateExpenseForToolTip(rowNode, monthlyData, 'projection1')"
                                            [innerHTML]="getSeparateExpenseForToolTip(rowNode, monthlyData, 'projection1')"
                                          ></div>
                                        </ng-template>
                                      </span>
                                    </td>
                                    <ng-template #monthlyValueExceptPosition>
                                      <ng-container
                                        *ngIf="rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit'; else monthlyValueExceptNetMargin"
                                      >
                                        <td *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                          {{
                                            rowNode?.level === 0
                                              ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                ? '$' + (monthlyData?.value | addCommasToNumbers)
                                                : (monthlyData?.value | addCommasToNumbers) + '%'
                                              : rowNode?.parent?.data?.type !== 'Gross Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers)
                                          }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                        </td>
                                      </ng-container>
                                      <ng-template #monthlyValueExceptNetMargin>
                                        <td>
                                          {{
                                            rowNode?.level === 0
                                              ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                ? '$' + (monthlyData?.value | addCommasToNumbers)
                                                : (monthlyData?.value | addCommasToNumbers) + '%'
                                              : rowNode?.parent?.data?.type !== 'Gross Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers)
                                          }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                        </td>
                                      </ng-template>
                                    </ng-template>
                                  </ng-template>
                                </ng-container>
                                <ng-container>
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                      else monthlyValueExceptNetMargin
                                    "
                                  >
                                    <td class="font-weight-bold p-r-15" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 1) | addCommasToNumbers) + '%'
                                          : '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                      }}
                                    </td>
                                  </ng-container>
                                  <ng-template #monthlyValueExceptNetMargin>
                                    <td class="font-weight-bold p-r-15">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 1) | addCommasToNumbers) + '%'
                                          : rowNode.parent.data.type !== 'Gross Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                      }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                    </td>
                                  </ng-template>
                                </ng-container>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="frozenbody" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container>
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                      else nameAcceptingNetMargin
                                    "
                                  >
                                    <td [ngClass]="getStyle(rowNode)" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                      <span [title]="rowData?.type"> {{ rowData?.type }} </span>
                                    </td>
                                    <td [title]="rowData?.name" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      {{ rowData?.name }}
                                    </td>
                                  </ng-container>
                                  <ng-template #nameAcceptingNetMargin>
                                    <td [ngClass]="getStyle(rowNode)">
                                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                      <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                    </td>
                                    <td [title]="rowData?.name">
                                      {{ rowData?.name }}
                                    </td>
                                  </ng-template>
                                </ng-container>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                              <tr *ngIf="!loading$">
                                <td colspan="4" class="center-align">No Data found.</td>
                              </tr>
                            </ng-template>
                          </p-treeTable>
                        </div>
                        <ng-template #resize_table>
                          <div class="scrollable-content">
                            <p-treeTable
                              [value]="projectionData1"
                              *isFetchingData="loading$"
                              [scrollable]="true"
                              [scrollHeight]="height"
                              [columns]="tableHeaders"
                              (onNodeExpand)="makeRowsSameHeight()"
                              (onNodeCollapse)="makeRowsSameHeight()"
                            >
                              <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                  <col style="width: 100px" />
                                  <col style="width: 100px" />
                                  <col *ngFor="let col of columns" style="width: 120px" />
                                  <col style="width: 120px" />
                                </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header" let-columns>
                                <tr>
                                  <th></th>
                                  <th></th>
                                  <th *ngFor="let col of columns">
                                    {{ col.monthLabel }}
                                  </th>
                                  <th>Total</th>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                                <tr style="height: 57px">
                                  <td [ngClass]="getStyle(rowNode)">
                                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                    <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                  </td>
                                  <td [title]="rowData?.name">
                                    {{ rowData?.name }}
                                  </td>
                                  <td
                                    *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder"
                                    [ngClass]="{
                                      pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                                    }"
                                  >
                                    <span
                                      *ngIf="
                                        rowNode?.node?.data?.type === 'SG&A' ||
                                          rowNode?.node?.data?.type === 'Revenue Adjustment' ||
                                          rowNode?.node?.data?.type === 'COGS Adjustment';
                                        else monthlyValues
                                      "
                                    >
                                      {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                    </span>
                                    <ng-template #monthlyValues>
                                      <span
                                        [ngClass]="{
                                          pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                                        }"
                                        [title]="rowNode.level === 2 && rowNode.parent.data.type === 'Bench' ? 'Click me for position details' : ''"
                                      >
                                        {{
                                          rowNode?.level === 0
                                            ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers) + '%'
                                            : rowNode?.parent?.data?.type !== 'Gross Margin'
                                            ? '$' + (monthlyData?.value | addCommasToNumbers)
                                            : (monthlyData?.value | addCommasToNumbers)
                                        }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                      </span>
                                    </ng-template>
                                  </td>
                                  <td class="font-weight-bold text-right">
                                    {{
                                      rowNode?.level === 0
                                        ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 1) | addCommasToNumbers) + '%'
                                        : rowNode.parent.data.type !==
                                          'Gross
                                  Margin'
                                        ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                        : (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                    }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                  </td>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                <tr *ngIf="!loading$">
                                  <td colspan="4" class="center-align">No Data found.</td>
                                </tr>
                              </ng-template>
                            </p-treeTable>
                          </div>
                        </ng-template>
                      </div>
                    </div>
                    <div data-wizard-type="step-content">
                      <div class="card-body pt-1">
                        <div *ngIf="!resizeFlag; else resize_table">
                          <p-treeTable
                            [value]="projectionData2"
                            *isFetchingData="loading$"
                            frozenWidth="390px"
                            [frozenColumns]="frozenCols"
                            [scrollable]="true"
                            [columns]="tableHeaders"
                            (onNodeExpand)="makeRowsSameHeight()"
                            (onNodeCollapse)="makeRowsSameHeight()"
                          >
                            <ng-template pTemplate="colgroup" let-columns>
                              <colgroup>
                                <col *ngFor="let col of columns" style="width: 120px" />
                                <col style="width: 120px" />
                              </colgroup>
                            </ng-template>
                            <ng-template pTemplate="header" let-columns>
                              <tr>
                                <th *ngFor="let col of columns">
                                  {{ col.monthLabel }}
                                </th>
                                <th class="p-r-15">Total</th>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder">
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'SG&A' || rowNode?.node?.data?.type === 'Revenue Adjustment' || rowNode?.node?.data?.type === 'COGS Adjustment';
                                      else monthlyValues
                                    "
                                  >
                                    <ng-container *ngIf="rowNode?.node?.data?.type !== 'SG&A'; else sgaPermissionBlock">
                                      <td>
                                        {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                      </td>
                                    </ng-container>

                                    <ng-template #sgaPermissionBlock>
                                      <ng-container *hasAnyPermission="permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                        <td>
                                          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                        </td>
                                      </ng-container>
                                    </ng-template>
                                  </ng-container>
                                  <ng-template #monthlyValues>
                                    <td *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'; else monthlyValueExceptPosition">
                                      <span>
                                        <span
                                          class=""
                                          *ngIf="rowNode?.parent?.parent?.data?.type === 'Projects'"
                                          [ngbTooltip]="tipContent"
                                          tooltipClass="negative-bench-tooltip"
                                          placement="auto"
                                          appendTo="body"
                                        >
                                          {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                        </span>
                                        <ng-template #tipContent>
                                          <div
                                            *ngIf="getSeparateExpenseForToolTip(rowNode, monthlyData, 'projection1')"
                                            [innerHTML]="getSeparateExpenseForToolTip(rowNode, monthlyData, 'projection1')"
                                          ></div>
                                        </ng-template>
                                      </span>
                                    </td>
                                    <ng-template #monthlyValueExceptPosition>
                                      <ng-container
                                        *ngIf="rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit'; else monthlyValueExceptNetMargin"
                                      >
                                        <td *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                          {{
                                            rowNode?.level === 0
                                              ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                ? '$' + (monthlyData?.value | addCommasToNumbers)
                                                : (monthlyData?.value | addCommasToNumbers) + '%'
                                              : rowNode?.parent?.data?.type !== 'Gross Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers)
                                          }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                        </td>
                                      </ng-container>
                                      <ng-template #monthlyValueExceptNetMargin>
                                        <td>
                                          {{
                                            rowNode?.level === 0
                                              ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                                ? '$' + (monthlyData?.value | addCommasToNumbers)
                                                : (monthlyData?.value | addCommasToNumbers) + '%'
                                              : rowNode?.parent?.data?.type !== 'Gross Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers)
                                          }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                        </td>
                                      </ng-template>
                                    </ng-template>
                                  </ng-template>
                                </ng-container>

                                <ng-container>
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                      else monthlyValueExceptNetMargin
                                    "
                                  >
                                    <td class="font-weight-bold p-r-15" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 1) | addCommasToNumbers) + '%'
                                          : '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                      }}
                                    </td>
                                  </ng-container>
                                  <ng-template #monthlyValueExceptNetMargin>
                                    <td class="font-weight-bold p-r-15">
                                      {{
                                        rowNode?.level === 0
                                          ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                            ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                            : (getTotal(rowData, rowNode, 1) | addCommasToNumbers) + '%'
                                          : rowNode.parent.data.type !== 'Gross Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 1) | addCommasToNumbers)
                                      }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                    </td>
                                  </ng-template>
                                </ng-container>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="frozenbody" let-rowNode let-rowData="rowData">
                              <tr style="height: 57px">
                                <ng-container>
                                  <ng-container
                                    *ngIf="
                                      rowNode?.node?.data?.type === 'Net Margin' || rowNode?.node?.data?.type === 'Net Profit' || rowNode?.node?.data?.type === 'SG&A';
                                      else nameAcceptingNetMargin
                                    "
                                  >
                                    <td [ngClass]="getStyle(rowNode)" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                      <span [title]="rowData?.type"> {{ rowData?.type }} </span>
                                    </td>
                                    <td [title]="rowData?.name" *hasAnyPermission="this.permissionModules.VIEW_NET_MARGIN; hideTemplate: true">
                                      {{ rowData?.name }}
                                    </td>
                                  </ng-container>
                                  <ng-template #nameAcceptingNetMargin>
                                    <td [ngClass]="getStyle(rowNode)">
                                      <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                      <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                    </td>
                                    <td [title]="rowData?.name">
                                      {{ rowData?.name }}
                                    </td>
                                  </ng-template>
                                </ng-container>
                              </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                              <tr *ngIf="!loading$">
                                <td colspan="4" class="center-align">No Data found.</td>
                              </tr>
                            </ng-template>
                          </p-treeTable>
                        </div>
                        <ng-template #resize_table>
                          <div class="scrollable-content">
                            <p-treeTable
                              [value]="projectionData2"
                              *isFetchingData="loading$"
                              [scrollable]="true"
                              [scrollHeight]="height"
                              [columns]="tableHeaders"
                              (onNodeExpand)="makeRowsSameHeight()"
                              (onNodeCollapse)="makeRowsSameHeight()"
                            >
                              <ng-template pTemplate="colgroup" let-columns>
                                <colgroup>
                                  <col style="width: 100px" />
                                  <col style="width: 100px" />
                                  <col *ngFor="let col of columns" style="width: 120px" />
                                  <col style="width: 120px" />
                                </colgroup>
                              </ng-template>
                              <ng-template pTemplate="header" let-columns>
                                <tr>
                                  <th></th>
                                  <th></th>
                                  <th *ngFor="let col of columns">
                                    {{ col.monthLabel }}
                                  </th>
                                  <th>Total</th>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
                                <tr style="height: 57px">
                                  <td [ngClass]="getStyle(rowNode)">
                                    <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
                                    <span [title]="rowData?.type">{{ rowData?.type }}</span>
                                  </td>
                                  <td [title]="rowData?.name">
                                    {{ rowData?.name }}
                                  </td>
                                  <td
                                    *ngFor="let monthlyData of getMonthlyValues(rowData, rowNode) | keyvalue : preserveOriginalOrder"
                                    [ngClass]="{
                                      pointer: rowNode.level === 2 && rowNode.parent.data.type === 'Bench'
                                    }"
                                  >
                                    <span
                                      *ngIf="
                                        rowNode?.node?.data?.type === 'SG&A' ||
                                          rowNode?.node?.data?.type === 'Revenue Adjustment' ||
                                          rowNode?.node?.data?.type === 'COGS Adjustment';
                                        else monthlyValues
                                      "
                                    >
                                      {{ '$' + (monthlyData?.value | addCommasToNumbers) }}
                                    </span>
                                    <ng-template #monthlyValues>
                                      <span>
                                        {{
                                          rowNode?.level === 0
                                            ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                              ? '$' + (monthlyData?.value | addCommasToNumbers)
                                              : (monthlyData?.value | addCommasToNumbers) + '%'
                                            : rowNode?.parent?.data?.type !== 'Gross Margin'
                                            ? '$' + (monthlyData?.value | addCommasToNumbers)
                                            : (monthlyData?.value | addCommasToNumbers)
                                        }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
                                      </span>
                                    </ng-template>
                                  </td>
                                  <td class="font-weight-bold text-right">
                                    {{
                                      rowNode?.level === 0
                                        ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
                                          ? '$' + (getTotal(rowData, rowNode, 2) | addCommasToNumbers)
                                          : (getTotal(rowData, rowNode, 2) | addCommasToNumbers) + '%'
                                        : rowNode.parent.data.type !==
                                          'Gross
                                  Margin'
                                        ? '$' + (getTotal(rowData, rowNode, 2) | addCommasToNumbers)
                                        : (getTotal(rowData, rowNode, 2) | addCommasToNumbers)
                                    }}{{ rowNode?.level === 1 && rowNode.parent.data.type === 'Gross Margin' ? '%' : '' }}
                                  </td>
                                </tr>
                              </ng-template>
                              <ng-template pTemplate="emptymessage">
                                <tr *ngIf="!loading$">
                                  <td colspan="4" class="center-align">No Data found.</td>
                                </tr>
                              </ng-template>
                            </p-treeTable>
                          </div>
                        </ng-template>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
<p-dialog
  dismissableMask="true"
  backdrop="false"
  position="top-right"
  [(visible)]="showFilterListDialog"
  [modal]="true"
  class="filter-dialog"
  [draggable]="false"
  [resizable]="false"
>
  <ng-template pTemplate>
    <div class="filter-listing" *ngIf="showSavedFilter">
      <input type="search" class="form-control mb-2" placeholder="Search" (input)="searchFilters($event.target.value)" />
      <div class="title" *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">Shared Filters</div>
      <ng-container *hasAnyPermission="permissionModules.VIEW_SHARE_FILTER; hideTemplate: true">
        <span *ngIf="sharedFilters?.length; else noData">
          <div
            class="form-check filter-body"
            *ngFor="let filterOption of sharedFilters"
            [ngClass]="{
              'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
            }"
          >
            <label class="form-check-label">
              <input
                [formControl]="selectedFilterFormControl"
                (ngModelChange)="applySelectedFilterAndUpdateUrl()"
                type="radio"
                class="form-check-input custom-radio"
                [value]="filterOption"
                name="filteroption"
              />{{ filterOption?.query_filter?.name }}
            </label>
            <div class="filter-icons action-icons">
              <ng-container *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true">
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
                  <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary share-icon" (click)="unShareFilter(filterOption)">
                  <em class="fa-solid fa-share" title="UnShare Filter"></em>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
                </a>
                <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
                  <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
                </a>
              </ng-container>
            </div>
          </div>
        </span>
      </ng-container>

      <ng-template #noData>
        <div>-</div>
      </ng-template>
      <div class="title">My Filters</div>
      <span *ngIf="myFilters?.length; else noData">
        <div
          class="form-check filter-body"
          *ngFor="let filterOption of myFilters"
          [ngClass]="{
            'selected-filter': filterOption?.query_filter?.id === selectedFilterFormControl?.value?.query_filter?.id
          }"
        >
          <label class="form-check-label">
            <input
              [formControl]="selectedFilterFormControl"
              (ngModelChange)="applySelectedFilterAndUpdateUrl()"
              type="radio"
              class="form-check-input custom-radio"
              [value]="filterOption"
              name="filteroption"
            />{{ filterOption?.query_filter?.name }}
          </label>
          <div class="filter-icons action-icons">
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary copy-icon" (click)="copyLinkToTheFilter(filterOption?.query_filter?.id)">
              <em title="Copy Filter Link" class="fa-regular fa-copy"></em>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              (click)="shareFilter(filterOption)"
              *hasAnyPermission="permissionModules.MANAGE_SHARE_FILTER; hideTemplate: true"
            >
              <em class="fa-solid fa-share" title="Share Filter"></em>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="editFilter(filterOption)"> </span>
            </a>
            <a class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary">
              <span title="Delete" [inlineSVG]="'assets/media/svg/icons/delete.svg'" cacheSVG="true" class="svg-icon svg-icon-md" (click)="deleteFilter(filterOption)"> </span>
            </a>
          </div>
        </div>
      </span>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Rename" [(visible)]="showEditDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div class="form-group first" *ngIf="editFilterObj">
    <input type="text" pInputText class="form-control custom" [(ngModel)]="editFilterObj.query_filter.name" (input)="inputFilterName()" />
    <small *ngIf="showNameError" class="form-text text-danger"> Name is required</small>
  </div>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">Cancel</button>
      <button type="button" class="btn-save" (click)="applySelectedFilterAndUpdateUrl()" [disabled]="!(selectedFilter?.length > 0)" [isSubmitting]="isSubmitting">Apply</button>
      <button type="button" class="btn-save" (click)="saveEditFilter()" [isSubmitting]="isSubmitting">Update</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog header="Delete" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">Do you want to delete "{{ deleteFilterObj?.query_filter?.name }}" Filter?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveDeleteFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog [header]="shareFilterObj?.header" [(visible)]="showShareDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0 delete-title">{{ shareFilterObj?.text }}</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="saveShareFilter()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  header=""
  [(visible)]="showPausedProjectDialog"
  [modal]="true"
  class="confirm-dialog-paushed"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
  [closable]="false"
  [style]="{ width: '725px' }"
>
  <ng-container *ngIf="!isResumeValidationInProgress; else noData">
    <div class="ml-2">
      <div class="d-flex align-items-center">
        <h5 class="p-m-0 mb-3 text-wrap">The following project’s financial calculations are paused while edits are in progress</h5>
      </div>
      <p-table
        class="paused-project-table"
        [resizableColumns]="true"
        [lazy]="true"
        [style]="{ overflow: 'auto!important' }"
        #dt
        [value]="pausedProjectList"
        responsiveLayout="scroll"
      >
        <ng-template pTemplate="header">
          <tr class="sticky-row-1">
            <th>Project</th>
            <th>Client</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Status</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
          <tr>
            <td>{{ rowData?.project?.name }}</td>
            <td>{{ rowData?.project?.customer?.name }}</td>
            <td>{{ rowData?.project?.start_date | date : 'dd/MM/yyyy' }}</td>
            <td>{{ rowData?.project?.end_date | date : 'dd/MM/yyyy' }}</td>
            <td>{{ rowData?.project?.status }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <ng-template pTemplate="footer">
      <div class="d-flex flex-nowrap justify-content-end align-items-center">
        <button type="button" class="btn-cancel" (click)="wait()" [disabled]="isResumeValidationInProgress">Recalculate Now</button>
        <button type="button" class="btn-save" (click)="continue()" [disabled]="isResumeValidationInProgress">Proceed using previous financial calculations</button>
      </div>
    </ng-template>
  </ng-container>
  <ng-template #noData>
    <div class="ml-2">
      <div class="project-name">
        <h5 class="p-m-0 mb-3 text-wrap">
          Calculating cost for <span class="paused-project-name">{{ calculatingProjectName }}</span> Project...
        </h5>
      </div>
      <span [ngClass]="{ 'recalculating-message': isResumeValidationInProgress }" *ngIf="isResumeValidationInProgress">
        <i class="material-icons rotating-icon mr-2">hourglass_empty</i>
        <ng-container>{{ rotatingMessage }}...</ng-container>
      </span>
    </div>
  </ng-template>
</p-dialog>

<ng-template #colValueExceptNetMargin let-rowNode="rowNode" let-rowData="rowData" let-monthlyData="monthlyData">
  <div class="d-block cursor-pointer" [pTooltip]="showToolTip(monthlyData, rowNode, 'projection2')" [tooltipPosition]="'right'" appendTo="body" [escape]="false">
    <div>
      {{
        rowNode?.level === 0
          ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
            ? '$' + (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
            : (monthlyData?.value?.effective_date_2 | addCommasToNumbers) + '%'
          : rowNode?.parent?.data?.type !== 'Gross Margin'
          ? '$' + (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
          : (monthlyData?.value?.effective_date_2 | addCommasToNumbers)
      }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
    </div>
    <span *ngIf="monthlyData?.value?.diff == 0; else nonZeroMonthlyValues">
      {{ rowNode?.parent?.data?.type === 'Gross Margin' || rowNode?.node?.data?.type === 'Gross Margin' ? '0%' : '$' + (monthlyData?.value?.diff | addCommasToNumbers) }}
    </span>
    <ng-template #nonZeroMonthlyValues>
      <span
        [ngClass]="{
          green: getSymbol(monthlyData?.value?.diff, rowNode),
          red: !getSymbol(monthlyData?.value?.diff, rowNode)
        }"
      >
        {{
          rowNode?.level === 0
            ? rowNode?.node?.data?.type !== 'Gross Margin' && rowNode?.node?.data?.type !== 'Net Margin'
              ? '$' + (monthlyData?.value?.diff | addCommasToNumbers)
              : (monthlyData?.value?.diff | addCommasToNumbers) + '%'
            : rowNode?.parent?.data?.type !== 'Gross Margin'
            ? '$' + (monthlyData?.value?.diff | addCommasToNumbers)
            : (monthlyData?.value?.diff | addCommasToNumbers)
        }}{{ rowNode?.level === 1 && rowNode?.parent?.data?.type === 'Gross Margin' ? '%' : '' }}
        <span *ngIf="getSymbol(monthlyData?.value?.diff, rowNode); else downSymbol" class="green">
          <!-- if the cost goes up that would not be good and will show red up arrow; vice-a-versa -->
          <ng-container
            *ngIf="
              rowNode?.parent?.data?.type === appConstants.costOfGoodsSold ||
                rowNode?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                rowNode?.parent?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                rowNode?.node?.data?.type === appConstants.costOfGoodsSold;
              else upArrow
            "
          >
            <em class="fa-solid fa-sort-down"></em>
          </ng-container>
          <ng-template #upArrow>
            <em class="fa-solid fa-sort-up"></em>
          </ng-template>
        </span>
        <ng-template #downSymbol>
          <span class="red">
            <ng-container
              *ngIf="
                rowNode?.parent?.data?.type === appConstants.costOfGoodsSold ||
                  rowNode?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                  rowNode?.parent?.parent?.parent?.data?.type === appConstants.costOfGoodsSold ||
                  rowNode?.node?.data?.type === appConstants.costOfGoodsSold;
                else downArrow
              "
            >
              <em class="fa-solid fa-sort-up"></em>
            </ng-container>
            <ng-template #downArrow>
              <em class="fa-solid fa-sort-down"></em>
            </ng-template>
          </span>
        </ng-template>
      </span>
    </ng-template>
  </div>
</ng-template>
