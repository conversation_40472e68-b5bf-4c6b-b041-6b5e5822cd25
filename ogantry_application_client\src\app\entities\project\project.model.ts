import { CustomerContact } from '@shared/models';
import { Employee as EmployeeData } from '../administration/administration.model';

export interface Contact {
  contact?: CustomerContact;
}

export enum BillingTypes {
  MONTHLY_RETAINER = 'Retainer',
  FIXED_BID = 'Fixed Bid',
  TEAM_RATE = 'Rate',
  TIME_MATERIALS = 'Time and Materials',
  WEEKLY_TEAM_RATE = 'Weekly',
  MONTHLY_TEAM_RATE = 'Monthly'
}

export interface BillingType {
  billing_types?: string;
  message?: string;
  success?: boolean;
}

export interface Validation {
  project: {
    id: number;
    validated: string;
    name: string;
    last_validated_date: string;
    extended_fields?: any;
  };
}

export interface Project {
  project?: {
    amount?: number;
    billing_type?: string;
    description?: string;
    start_date?: string;
    end_date?: string;
    effective_start_date?: string;
    effective_end_date?: string;
    fixed_bid_price?: string;
    name?: string;
    status?: string;
    id?: number;
    position?: string;
    customer?: {
      name?: string;
      ref?: string;
      id?: number;
    };
    extended_fields: any;
    customer_id?: string;
    region_id?: string;
    contacts?: Contact[];
    projection?: Projection;
    bill_rate?: string;
    tags?: string[];
    validated?: string;
  };
  isEdit?: boolean;
}

export interface ValidationData {
  id?: number;
  project: {
    id: number;
  };
  status?: string;
  total_contract_cost?: string;
  total_cost?: string;
  total_direct_cost?: string;
  total_expenses?: string;
  total_gross_margin?: string;
  total_open_positions_cost?: string;
  total_percent_gross_margin?: string;
  total_revenue?: string;
}

export interface ProjectList {
  data?: {
    projects: Project[];
  };
  message?: string;
  success?: boolean;
}

export interface SaveFilter {
  is_shared?: boolean;
  resource?: string;
  query_string?: string;
  name?: string;
}

export interface QueryFilter {
  query_filter: {
    id: number;
    is_shared: boolean;
    query_string: string;
    ref: string;
    resource: string;
    user_id: number;
    name: string;
  };
}
export interface ISavedFilterList {
  data: {
    query_filters?: QueryFilter[];
  };
}
export interface ISavedFilter {
  data: QueryFilter;
}

export class IFilter {
  name_search?: string;
  statuses?: string;
  customer_search?: string;
  position_date?: Date;
  limit?: number;
  offset?: number;
  order_by?: string;
  start_date?: Date;
  end_date?: Date;
  start_date_gte?: Date;
  end_date_gte?: Date;
  start_date_lt?: Date;
  end_date_lt?: Date;
  selectedFilterStartDate?: string;
  selectedFilterEndDate?: string;
  tags?: string;
  billingType?: string;
  isStartFromToday?: boolean;
  isEndFromToday?: boolean;
}
export interface QueryFilterParams {
  limit?: number;
  offset?: number;
  order_by?: string;
  name?: string;
  status?: string;
  customer_name?: string;
}

export interface ProjectProjections {
  employees_plus_work_exceptions?: EmployeeData[];
  projects?: Project[];
  total?: Total;
  totals_monthly?: MonthlyTotal[];
  utilizations?: any[];
}

export interface Total {
  contract_cost?: string;
  cost?: string;
  cost_less_unutilized?: string;
  direct_cost?: string;
  expense_plugs?: string;
  expenses?: string;
  gross_margin?: string;
  net_profit?: string;
  open_positions_cost?: string;
  percent_gross_margin?: string;
  percent_net_profit?: string;
  revenue?: string;
  revenue_plugs?: string;
  sga_plugs?: string;
  unutilized_cost?: string;
  work_exception_cost?: string;
}

export interface MonthlyTotal {
  monthly_total: {
    contract_cost: string;
    cost: string;
    cost_less_unutilized: string;
    direct_cost: string;
    expense_plugs: string;
    expenses: string;
    gross_margin: string;
    month: number;
    net_profit: string;
    open_positions_cost: string;
    percent_gross_margin: string;
    percent_net_profit: string;
    revenue: string;
    revenue_plugs: string;
    sga_plugs: string;
    unutilized_cost: string;
    work_exception_cost: string;
    year: number;
  };
}

export interface LatestValidation {
  data: ValidationData;
  message: string;
  success: boolean;
}
export interface Projections {
  projection?: Projection;
}

export interface Projection {
  cost?: string;
  percent_gross_margin?: number;
  revenue?: string;
  total_expenses?: string;
  gross_margin?: number;
  total_open_positions_cost?: number;
  valid_monthly_projections?: Array<ValidMonthlyProjections>;
  labour_cost?: number;
  profit?: number;
  total_contract_cost?: string;
  total_direct_cost?: string;
}

export interface ValidMonthlyProjections {
  valid_monthly_projection: ValidMonthlyProjection;
}

export interface ValidMonthlyProjection {
  contract_cost?: number;
  cost?: number;
  direct_cost?: number;
  expenses?: number;
  month?: number;
  year?: number;
  open_positions_cost?: number;
  revenue?: number;
  validated_monthly_positions?: Array<ValidatedMonthlyPositions>;
}

export interface ValidatedMonthlyPositions {
  validated_monthly_position: ValidatedMonthlyProjection;
}

export interface ValidatedMonthlyProjection extends ValidMonthlyProjection {
  position?: PositionRef;
}

export interface PositionRef {
  name: string;
  ref?: string;
  id?: Number;
  employee?: EmployeeRef;
}

export interface EmployeeRef {
  first_name?: string;
  id?: number;
  last_name?: string;
}

export interface Employees {
  employees?: Employee[];
}

export interface Employee {
  employee?: {
    id?: number;
    name?: string;
    first_name?: string;
    last_name?: string;
    hourly_cost?: string;
  };
  label?: string;
  value?: string;
  id?: number;
  cost?: string;
}

export interface ExtendedFields {
  [key: string]: string;
}

export class Position {
  position?: {
    id?: number;
    pid?: number;
    name?: string;
    bill_rate?: string;
    cost?: string;
    start_date?: string;
    end_date?: string;
    project_id?: number;
    daily_billable_hours?: number;
    weekly_billable_hours?: number;
    employee_id?: number;
    purchase_order_id?: number;
    date?: Date[];
    employee?: {
      id?: any;
      first_name?: string;
      last_name?: string;
      ref?: string;
    };
    daily_expenses?: DailyExpense[];
    monthly_expenses?: MonthlyExpense[];
    type?: string;
    position_type_id?: any;
    extended_fields?: ExtendedFields;
    project?: {
      name?: string;
      ref?: string;
      id?: number;
      customer: {
        id: 3;
        name: string;
      };
    };
    total?: {
      actual_hours?: string;
      billable_hours?: string;
      variance?: string;
      percent_gross_margin?: string;
      expenses?: string;
    };
    margin?: number;
    expense?: number;
    purchase_order?: PurchaseOrder;
  };
}

export interface PositionList {
  positions?: Position[];
}

export class AddExpense {
  id?: number;
  cost?: string;
  description?: string;
  employee_id?: number;
  position_id?: number;
  type_id?: number;
  month?: number;
  year?: number;
  date?: Date;
  type?: string;
}

export interface DailyExpenseTypeList {
  daily_expense_types?: DailyExpenseType[];
}

export interface DailyExpenseType {
  daily_expense_type?: {
    id?: number;
    name?: string;
  };
  label?: string;
  value?: number;
}

export interface MonthlyExpenseTypeList {
  monthly_expense_types?: MonthlyExpenseType[];
}

export interface MonthlyExpenseType {
  monthly_expense_type?: {
    id?: number;
    name?: string;
  };
  label?: string;
  value?: number;
}

export interface DailyExpenses {
  position_daily_expenses?: DailyExpense[];
}

export interface DailyExpense {
  position_daily_expense?: {
    cost?: string;
    description?: string;
    id?: number;
    type?: {
      id?: number;
      name?: string;
    };
  };
}

export interface MonthlyExpenses {
  monthly_expenses?: MonthlyExpense[];
}

export interface MonthlyExpense {
  position_monthly_expense?: {
    cost?: string;
    description?: string;
    id?: number;
    month?: number;
    year?: number;
    type?: {
      id?: number;
      name?: string;
    };
  };
}

export interface ProjectDailyExpenses {
  project_daily_expenses?: ProjectDailyExpense[];
}

export interface ProjectDailyExpense {
  project_daily_expense?: {
    cost?: string;
    description?: string;
    id?: number;
    type?: {
      id?: number;
      name?: string;
    };
  };
}

export interface ProjectMonthlyExpenses {
  project_monthly_expenses?: ProjectMonthlyExpense[];
}

export interface ProjectMonthlyExpense {
  project_monthly_expense?: {
    cost?: string;
    description?: string;
    id?: number;
    month?: number;
    year?: number;
    type?: {
      id?: number;
      name?: string;
    };
  };
}

export class PositionExpenseList {
  id?: number;
  cost?: string;
  description?: string;
  type?: {
    id?: number;
    name?: string;
  };
  month?: number;
  year?: number;
  key?: string;
}

export interface ProjectStatusData {
  id: number;
  is_default: boolean;
  name: string;
  order: number;
}

export interface ProjectStatus {
  project_status: ProjectStatusData;
}

export interface ProjectStatusResponse {
  project_statuses: ProjectStatus[];
}

export interface FixedBidPlugList {
  fixed_bid_plugs?: FixedBidPlug[];
  retainer_plugs?: FixedBidPlug[];
}

export interface FixedBidPlug {
  fixed_bid_plug: {
    amount?: String;
    id?: Number;
    month?: Number;
    project?: {
      name?: String;
    };
    reason?: String;
    year?: Number;
  };
}

export interface EffectiveDates {
  dates?: [];
}

export interface ProjectStartEndDateObj {
  start_date: string | Date;
  end_date: string | Date;
}

export interface DataFilterForPositions {
  name?: string;
  bill_rate?: string;
  cost?: string;
  start_date?: string | Date;
  end_date?: string | Date;
  daily_billable_hours?: number;
  weekly_billable_hours?: number;
  employee_id?: number;
  date?: Date[];
  position_type_id?: string | number;
  selectedFilterStartDate?: string;
  selectedFilterEndDate?: string;
  order_by?: string;
}

export interface PurchaseOrderList {
  purchase_orders: PurchaseOrderObj[];
}

export interface PurchaseOrderObj {
  purchase_order: PurchaseOrder;
}

export interface PurchaseOrder {
  id?: number;
  description?: string;
  customer_id?: number;
  amount?: string;
  po_number?: string;
  termination_date?: string;
}

export interface SelectedMonth {
  month: number;
  year: number;
}

export interface POListObj {
  label: string;
  id: number;
}

export interface UpdateMultiPOObj {
  position_ids: string;
  purchase_order_number: string;
}

export interface UpdateMultiPosDate {
  position_ids: string;
  start_date: string;
  end_date: string;
}
